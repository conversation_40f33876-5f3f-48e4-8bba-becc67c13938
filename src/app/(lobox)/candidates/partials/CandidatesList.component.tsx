import EmptyState from '@shared/components/Organism/EmptyState/EmptyState';
import SearchListWithDetailsLayout from '@shared/components/layouts/SearchListWithDetailsLayout';
import CandidateListItem from '@shared/components/molecules/CandidateCard/CandidateListItem';
import { CandidateFormStepKeys } from '@shared/components/Organism/MultiStepForm/CreateCandidateForm/constants';
import SearchList from '@shared/components/Organism/SearchList';
import eventKeys from '@shared/constants/event-keys';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import Button from '@shared/uikit/Button';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Switch from '@shared/uikit/Switch';
import { searchCandidate } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import event from '@shared/utils/toolkit/event';
import React, { useCallback, useEffect } from 'react';
import type { ICandidateListItemProps } from '@shared/types/candidates';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import { useRemoteNavigationControl } from '@shared/stores/remotePaginationStore/store';
import Skeleton from '@shared/uikit/Skeleton';
import pickBy from 'lodash/pickBy';
import useCandidateFilters from '@shared/hooks/useCandidateFilters';
import SearchFiltersModalSkeleton from '@shared/components/Organism/SearchFiltersModal/SearchFiltersModal.skeleton';
import dynamic from 'next/dynamic';
import {
  useSearchDispatch,
  useSearchState,
} from '@shared/contexts/search/search.provider';
import useSearchQueries from '@shared/hooks/useSearchQueries';
import { searchFilterQueryParams } from '@shared/constants/search';
import CandidateSavedSearchFilters from '@shared/components/Organism/CandidateSavedSearchFilters';
import CandidateDetails from './CandidateDetails.component';
import CandidateDetailsSkeleton from './CandidateDetails.skeleton';
import classes from './CandidatesList.module.scss';
import type { CandidateFiltersDataProps } from './hooks/useCandidateSearch';
import useCandidateSearch from './hooks/useCandidateSearch';

interface CandidatesListProps {
  searchId?: string;
  setSearchId?: (id?: string) => void;
  filtersData?: CandidateFiltersDataProps;
  filterLoading?: boolean;
}

const SaveSearchFiltersModal = dynamic(
  () => import('@shared/components/Organism/SaveSearchFiltersModal'),
  { ssr: false, loading: () => <SearchFiltersModalSkeleton /> }
);

const CandidatesList: React.FC<CandidatesListProps> = ({
  searchId,
  filtersData,
  setSearchId,
  filterLoading,
}) => {
  const { t } = useTranslation();
  const { groups, savedFiltersGroups } = useCandidateSearch(filtersData);
  const searchDispatch = useSearchDispatch();
  const isSaveSearchFilterModalOpen = useSearchState(
    'saveSearchFilterModalData'
  )?.isOpen;
  const { filters, currentEntityId, shouldRefresh } = useCandidateFilters();
  const { handleChangeParams, checkHasFilters, allParams } = useCustomParams();
  const { onlyApplicants } = filters;
  const { getQueryValue } = useSearchQueries();
  const visibleSavedFilters =
    getQueryValue(searchFilterQueryParams.candidateSearchType) === 'saved';
  const hasFilters = checkHasFilters(['currentEntityId']);

  const toggleSaveFilterModal = (isOpen: boolean) => {
    searchDispatch({
      type: 'SET_SAVE_SEARCH_FILTER_MODAL_DATA',
      payload: {
        isOpen,
      },
    });
  };

  const closeSaveFilterHandler = () => toggleSaveFilterModal(false);

  useEffect(() => {
    if (isSaveSearchFilterModalOpen) {
      closeSaveFilterHandler();
    }
  }, []);

  useEffect(() => {
    event.trigger(eventKeys.scrollToTopFeedList);
  }, [currentEntityId]);

  const {
    totalElements,
    totalPages,
    content: candidates = [],
    isEmpty,
    isFetching,
    refetch,
    isLoading,
    searchId: querySearchId,
  } = usePaginateQuery<ICandidateListItemProps>({
    action: {
      apiFunc: searchCandidate,
      key: [QueryKeys.searchCandidates, filters],
      params: {
        currentEntityId,
        searchId,
        ...filters,
      },
    },
    config: {
      onSuccess: (data) => {
        if (data.searchId !== searchId) {
          setSearchId?.(data.searchId);
        }
      },
    },
  });

  useEffect(() => {
    if (querySearchId !== searchId) {
      setSearchId?.(querySearchId);
    }
  }, [searchId, querySearchId, setSearchId]);

  useEffect(() => {
    const firstEntityId = candidates[0]?.id;
    if (!isLoading && !currentEntityId && !!firstEntityId) {
      handleChangeParams({
        add: { currentEntityId: firstEntityId },
        remove: ['refresh'],
      });
    }
  }, []);

  useEffect(() => {
    if (shouldRefresh === 'true') {
      refetch();
      handleChangeParams({
        remove: ['refresh'],
      });
    }
  }, [shouldRefresh, refetch, handleChangeParams]);

  const handleOpenCreateModal = useCallback(() => {
    openMultiStepForm({
      formName: 'createCandidateForm',
      stepKey: CandidateFormStepKeys.NEW,
    });
  }, []);

  const updateQueryParams = useCallback(
    (params: Record<string, string | string[]>) => {
      handleChangeParams({
        replace: pickBy(params, Boolean),
        remove: ['page', 'currentEntityId'],
      });
    },
    [handleChangeParams]
  );
  const isLoadingAPis = isFetching || filterLoading;

  return (
    <>
      <SearchListWithDetailsLayout
        isFullWidth
        groups={groups}
        isLoading={isLoadingAPis}
        isEmpty={isEmpty && !isLoadingAPis}
        filterTopComponent={
          visibleSavedFilters ? (
            <CandidateSavedSearchFilters isLoading={isLoading} />
          ) : undefined
        }
        headerHeight={visibleSavedFilters ? 120 : undefined}
        isTotalyEmpty={
          !hasFilters && Boolean(!candidates.length && !isLoadingAPis)
        }
        sectionTotalEmpty={
          <CardWrapper
            classNames={{
              root: '!my-20 !h-full',
              container: '!h-full justify-center items-center',
            }}
          >
            <EmptySearchResult title={t('no_content_found')} />
          </CardWrapper>
        }
        customSetResult={updateQueryParams}
        headerComponents={
          <>
            <DividerVertical />
            <Button
              label={t('create')}
              leftIcon="plus"
              disabled={isLoadingAPis || filterLoading}
              onClick={() => handleOpenCreateModal()}
            />
          </>
        }
        listComponent={
          <SearchList
            entity="candidates"
            title={onlyApplicants ? t('applicants') : t('candidates')}
            isLoading={isLoadingAPis}
            totalElements={totalElements}
            data={candidates}
            emptyList={
              <EmptyState
                message={
                  onlyApplicants
                    ? t('no_applicants_found')
                    : t('no_candidates_found')
                }
                caption=""
                className="h-full flex justify-center items-center"
              />
            }
            totalPages={totalPages}
            scrollToTopWhenClick
            noItemButtonAction
            headerElement={
              isLoadingAPis ? (
                <Skeleton className="rounded !w-[120px] !h-[22px] !mt-[9px]" />
              ) : (
                <Switch
                  value={onlyApplicants}
                  onChange={() => {
                    handleChangeParams({
                      add: !onlyApplicants
                        ? { onlyApplicants: 'true' }
                        : undefined,
                      remove: onlyApplicants ? ['onlyApplicants'] : undefined,
                    });
                  }}
                  className={classes.headerSwitch}
                  label={t('applicants')}
                  labelProps={{ color: 'secondaryDisabledText', font: '400' }}
                />
              )
            }
            renderItem={(item) => (
              <CandidateListItem
                item={item}
                key={item.id}
                isFocused={item.id === currentEntityId}
                className={classes.listItem}
              />
            )}
          />
        }
        detailsComponent={
          isLoadingAPis || !currentEntityId ? (
            <CandidateDetailsSkeleton />
          ) : (
            <CandidateDetails
              key={currentEntityId}
              candidateId={currentEntityId}
            />
          )
        }
        hasBackBtn={false}
        isBusinessApp
      />
      {(isSaveSearchFilterModalOpen || false) && (
        <SaveSearchFiltersModal
          onClose={closeSaveFilterHandler}
          groups={savedFiltersGroups}
        />
      )}
    </>
  );
};

export default CandidatesList;
