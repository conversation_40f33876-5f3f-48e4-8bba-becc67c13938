import type { ButtonProps } from '@shared/uikit/Button';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex/index';
import React, { useMemo } from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Typography from '@shared/uikit/Typography';
import InfoCard from '@shared/uikit/InfoCard';
import { CompanyTab } from '@shared/types/company';
import useToast from '@shared/uikit/Toast/useToast';
import * as CompanyApi from '@shared/utils/api/company';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';

const CompanyActionButton = ({ company, activeTab, badges }) => {
  const { t } = useTranslation();
  const { openConfirmDialog } = useOpenConfirm({ variant: 'normal' });
  const toast = useToast();

  const { mutate: addClient } = useReactMutation({
    apiFunc: CompanyApi.addClient,
  });
  const { mutate: addVendor } = useReactMutation({
    apiFunc: CompanyApi.addVendor,
  });

  const { mutate: cancelRequest } = useReactMutation({
    apiFunc: CompanyApi.cancelRequest,
  });
  const { mutate: acceptRequest } = useReactMutation({
    apiFunc: CompanyApi.acceptRequest,
  });

  const handleAddAsClient = () => {
    openConfirmDialog({
      message: translateReplacer(t('confirm_add_company_as_relation'), [
        'client',
      ]),
      title: t('add_client'),
      confirmButtonText: t('confirm'),
      cancelButtonText: t('discard'),
      isAjaxCall: true,
      apiProps: {
        func: addClient,
        variables: {
          clientId: company.id,
        },
        onSuccess: () => {
          toast({
            type: 'success',
            icon: 'check-circle',
            message: translateReplacer(t('relation_added_successfully'), [
              'client',
            ]),
          });
        },
      },
    });
  };
  const handleAddAsVendor = () => {
    openConfirmDialog({
      message: translateReplacer(t('confirm_add_company_as_relation'), [
        'vendor',
      ]),
      title: t('add_vendor'),
      confirmButtonText: t('confirm'),
      cancelButtonText: t('discard'),
      isAjaxCall: true,
      apiProps: {
        func: addVendor,
        variables: {
          vendorId: company.id,
        },
        onSuccess: () => {
          toast({
            type: 'success',
            icon: 'check-circle',
            message: translateReplacer(t('relation_added_successfully'), [
              'vendor',
            ]),
          });
        },
      },
    });
  };
  const handleCancelRequest = () => {
    openConfirmDialog({
      message: t('confirm_cancel_request_to_company'),
      title: t('cancel_request'),
      confirmButtonText: t('confirm'),
      cancelButtonText: t('discard'),
      isAjaxCall: true,
      apiProps: {
        func: cancelRequest,
        variables: {
          id: company.vendorClientId,
        },
        onSuccess: () => {
          toast({
            type: 'success',
            icon: 'check-circle',
            message: t('job_published_successfully'),
          });
        },
      },
    });
  };
  const handleAcceptRequest = () => {
    acceptRequest({ id: currentEntityId }, { onSuccess: alert });
  };
  const handleDeclineRequest = () => {
    openConfirmDialog({
      message: translateReplacer(t('confirm_decline_pending_request'), [
        t('vendor'),
      ]),
      title: t('decline_request'),
      confirmButtonText: t('confirm'),
      cancelButtonText: t('discard'),
      isAjaxCall: true,
      apiProps: {
        func: cancelRequest,
        variables: {
          id: company.vendorClientId,
        },
        onSuccess: () => {
          toast({
            type: 'success',
            icon: 'check-circle',
            message: t('job_published_successfully'),
          });
        },
      },
    });
  };
  const handleSubmitJob = () => {
    openMultiStepForm({
      formName: 'submitJob',
      data: {
        step: '1',
        vendorId: company.id,
      },
    });
  };

  const handleSubmitCandidate = () => {
    console.log('openSubmitCandidateModal');
  };
  const handleContactCompany = () => {
    console.log('Contact company comming soon');
  };

  const actions = useMemo<ButtonProps[]>(() => {
    switch (activeTab) {
      case CompanyTab.VENDORS:
        return [
          {
            label: t('contact'),
            schema: 'semi-transparent',
            onClick: handleContactCompany,
          },
          {
            label: t('submit_job'),
            onClick: handleSubmitJob,
          },
        ];
      case CompanyTab.CLIENTS:
        return [
          {
            label: t('contact'),
            schema: 'semi-transparent',
            onClick: handleContactCompany,
          },
          {
            label: t('submit_candidate'),
            onClick: handleSubmitCandidate,
          },
        ];
      case CompanyTab.REQUESTS:
        return [
          {
            label: t('cancel_request'),
            schema: 'semi-transparent',
            onClick: handleCancelRequest,
          },
        ];
      case CompanyTab.PENDING:
        return [
          {
            label: t('decline'),
            schema: 'semi-transparent',
            onClick: handleDeclineRequest,
          },
          {
            label: t('accept'),
            onClick: handleAcceptRequest,
          },
        ];
      default:
        return [
          {
            label: t('add_as_client'),
            schema: 'semi-transparent',
            onClick: handleAddAsClient,
          },
          {
            label: t('add_as_vendor'),
            onClick: handleAddAsVendor,
          },
        ];
    }
  }, [activeTab]);

  return (
    <Flex className="gap-20">
      {badges}
      <InfoCard
        hasLeftIcon
        leftIconProps={{
          name: 'info-circle',
          color: 'secondaryDisabledText',
        }}
        classNames={{ wrapper: '!bg-gray_5' }}
      >
        <Typography size={14} color="secondaryDisabledText">
          {t('yve_req_t_b_a_v_ftc')}
        </Typography>
      </InfoCard>
      <Flex flexDir="row" className="gap-16">
        {actions.map((props, key) => (
          <Button {...props} key={`action-${key}`} fullWidth />
        ))}
      </Flex>
    </Flex>
  );
};

export default CompanyActionButton;
