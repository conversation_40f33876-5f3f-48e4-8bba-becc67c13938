import type { EmbedHTMLAttributes } from 'react';
import { appEnvironment } from '@shared/utils/constants';
import { useLanguage } from '@shared/contexts/Language';
import cnj from '../utils/cnj';
import useTheme from '../utils/useTheme';

export interface EmbededViewProps
  extends EmbedHTMLAttributes<HTMLIFrameElement> {
  src?: string;
  className?: string;
}
const baseUrl =
  'https://storage.googleapis.com/lobox_public_resumes/resume/lobox';

export function getEmbededDocumentLink(
  src: string,
  options?: { locale?: string }
) {
  const isInternalUrl = src.startsWith(baseUrl);
  const mirroredSrc = src.replace(
    baseUrl,
    '/lobox_public_resumes/resume/lobox'
  );
  const optionsParams = new URLSearchParams(options ?? {}).toString();
  const hash = optionsParams ? `#${optionsParams}` : '';
  const fullUrl =
    src.startsWith('http') || src.startsWith('blob')
      ? src
      : `${appEnvironment.baseUrl}${src}`;

  // Use the direct PDF URL instead of wrapping it in a PDF viewer
  const embededSrc = isInternalUrl
    ? `${appEnvironment.baseUrl}${mirroredSrc}${hash}`
    : fullUrl;

  return {
    mirroredSrc,
    embededSrc,
  };
}

export default function EmbededView({
  src,
  title,
  className,
  ...props
}: EmbededViewProps) {
  const { language: locale } = useLanguage();
  const { isDark } = useTheme();

  console.log('src', src);

  if (!src) return null;

  const { embededSrc } = getEmbededDocumentLink(src, { locale });

  console.log('embededSrc', embededSrc);

  return (
    <iframe
      title={title}
      className={cnj(
        'aspect-[4/3] overflow-auto scroll-smooth items-center',
        className
      )}
      {...props}
      src={embededSrc}
      style={{ colorScheme: isDark ? 'dark' : 'light' }}
      lang={locale}
    />
  );
}
