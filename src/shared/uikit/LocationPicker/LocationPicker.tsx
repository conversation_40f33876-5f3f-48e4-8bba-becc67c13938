import React, { useEffect, useRef, useState } from 'react';
import geoApi from 'shared/utils/api/geo';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from 'shared/uikit/AddLocationForm/index.module.scss';
import Form from 'shared/uikit/Form';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import SvgLocation from 'shared/uikit/shared/svg/SvgLocation';
import Flex from 'shared/uikit/Flex';
import cnj from 'shared/uikit/utils/cnj';
import DynamicFormBuilder from '../Form/DynamicFormBuilder';
import LocationAddressItem from '../LocationAddressItem';
import Button from '../Button';

interface LocationPickerProps {
  onChange: (e: any) => void;
  value: any;
  apiParams?: any;
  isAddLocationFormOpen?: boolean;
  setIsAddLocationFormOpen: (v: boolean) => void;
}

const LocationPicker = ({
  onChange,
  value = [],
  apiParams,
  isAddLocationFormOpen,
  setIsAddLocationFormOpen,
}: LocationPickerProps): JSX.Element => {
  const [selectedLocation, setSelectedLocation] = useState<any>(null);
  const isEdit = !!selectedLocation;
  const { t } = useTranslation();
  const formRef = useRef<any>();

  useEffect(() => {
    if (!value?.length) {
      setIsAddLocationFormOpen(true);
    }
  }, []);
  const onSuccess = ({ index, latLon, ...newLocation }: any) => {
    if (!isEdit) {
      onChange([...value, newLocation]);
    } else {
      const updatedValue = value?.map((item: any, i: number) =>
        i === index ? newLocation : item
      );
      onChange(updatedValue);
    }
    setIsAddLocationFormOpen(false);
  };

  const deleteHandler = (itemIndex: number) => () => {
    onChange?.(value.filter((_: any, index: number) => index !== itemIndex));
    setSelectedLocation(null);
  };
  const editHandler = (itemIndex: number) => () => {
    const address = value?.[itemIndex];
    setSelectedLocation({
      ...address,
      index: itemIndex,
    });
    setIsAddLocationFormOpen(true);
  };
  const addLocationHandler = () => {
    setSelectedLocation(null);
    setIsAddLocationFormOpen(true);
  };
  const onChangeHandler = (val: any, { form }: any) => {
    if (val?.value) {
      formRef.current = form;
      formRef?.current?.setFieldValue('latLon', val);
    }
  };

  return (
    <>
      {isAddLocationFormOpen ? (
        <Form
          initialValues={selectedLocation || {}}
          local
          onSuccess={onSuccess}
        >
          {() => (
            <>
              <Flex className={classes.svg}>
                <SvgLocation />
              </Flex>
              <DynamicFormBuilder
                groups={[
                  {
                    name: 'name',
                    cp: 'input',
                    maxLength: 100,
                    label: t('location_title'),
                    required: true,
                  },
                  {
                    apiFunc: geoApi.searchPlace,
                    cp: 'asyncAutoCompleteWithExtraParams',
                    label: t('address'),
                    required: true,
                    name: 'location',
                    autoComplete: 'nope',
                    wrapStyle: 'responsive-margin-top',
                    params: apiParams,
                    onChange: onChangeHandler,
                  },
                  {
                    name: 'primaryLocation',
                    cp: 'checkBox',
                    label: t('make_t_y_p_loc'),
                    wrapStyle: 'responsive-margin-top',
                    visibleOptionalLabel: false,
                  },
                  {
                    name: 'latLon',
                    cp: 'latLonPicker',
                    wrapStyle: cnj(
                      classes.latLanWrapper,
                      'responsive-margin-top'
                    ),
                    center: [41.0536, 28.982],
                    draggable: false,
                    zoom: 10,
                  },
                ].filter(Boolean)}
              />
              <ModalFooter className={classes.footer}>
                <Button
                  fullWidth
                  label={t('discard')}
                  schema="gray-semi-transparent"
                  onClick={() => setIsAddLocationFormOpen(false)}
                />
                <SubmitButton fullWidth label={t('add')} />
              </ModalFooter>
            </>
          )}
        </Form>
      ) : (
        <>
          {value?.map(
            ({ name, primaryLocation, location }: any, index: number) => (
              <LocationAddressItem
                key={location?.externalId + name}
                onEdit={editHandler(index)}
                onDelete={deleteHandler(index)}
                title={name}
                fullAddress={location.label}
                isPrimary={primaryLocation}
              />
            )
          )}
          <Button
            leftIcon="plus"
            labelFont="bold"
            schema="semi-transparent"
            label={t('add_location')}
            disabled={value?.length > 9}
            onClick={addLocationHandler}
          />
        </>
      )}
    </>
  );
};

export default LocationPicker;
