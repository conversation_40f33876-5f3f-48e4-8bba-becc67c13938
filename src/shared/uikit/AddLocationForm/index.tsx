import React, { type FC, useRef } from 'react';
import formValidator from 'shared/utils/form/formValidator';
import geoApi from 'shared/utils/api/geo';
import { locationZoomLevelValues } from 'shared/utils/constants/location';
import useTranslation from 'shared/utils/hooks/useTranslation';
import ModalForm from 'shared/uikit/ModalV1/ModalForm';
import cnj from 'shared/uikit/utils/cnj';
import { type RightSideProps } from '../ModalV1/ProfileBasicModal';
import SvgLocation from '../shared/svg/SvgLocation';
import useMedia from '../utils/useMedia';
import classes from './index.module.scss';

interface AddLocationFormProps<
  ModalComponent extends FC | undefined = undefined,
> {
  onClose: Function;
  onBack: Function;
  onSuccess: (args: any) => void;
  initialValues: any;
  confirmProps?: any;
  visiblePrimaryLocation?: boolean;
  visibleNameInput?: boolean;
  apiFunc?: Function;
  apiParams?: any;
  rightSideProps: RightSideProps<ModalComponent>;
  hideBack?: boolean;
}

export default function AddLocationForm<ModalComponent extends FC | undefined>({
  onClose,
  onBack,
  onSuccess,
  initialValues,
  confirmProps,
  visiblePrimaryLocation,
  visibleNameInput,
  apiFunc = geoApi.searchPlace,
  apiParams,
  rightSideProps,
  hideBack,
  ...rest
}: AddLocationFormProps<ModalComponent>) {
  const { isMoreThanTablet } = useMedia();
  const { t } = useTranslation();
  const formRef = useRef<any>();

  const onChangeHandler = (val: any, { form }: any) => {
    if (val?.value) {
      formRef.current = form;
      formRef?.current?.setFieldValue('latLon', val);
    }
  };
  return (
    <ModalForm
      confirmProps={confirmProps}
      onSuccess={onSuccess}
      onClose={onClose}
      onBack={onBack}
      hideBack={hideBack ?? isMoreThanTablet}
      title={t('add_location')}
      image={<SvgLocation />}
      initialValues={initialValues}
      validationSchema={formValidator.object().shape({
        location: formValidator
          .object()
          .test('value', 'select_one_of_sug_address', (val) => val?.value),
        description: formValidator.string().nullable().max(2000),
      })}
      local
      styles={{
        backdrop: classes.backdrop,
      }}
      groups={({ values }) =>
        [
          visibleNameInput && {
            name: 'name',
            cp: 'input',
            maxLength: 100,
            label: t('location_title'),
            required: true,
          },
          {
            apiFunc,
            cp: 'asyncAutoCompleteWithExtraParams',
            label: rightSideProps?.isFromRightSide
              ? rightSideProps?.inputLabel
              : t('address'),
            required: true,
            name: 'location',
            autoComplete: 'nope',
            wrapStyle: rightSideProps?.isFromRightSide
              ? classes.unsetFlexBasis
              : 'responsive-margin-top',
            params: apiParams,
            onChange: onChangeHandler,
          },
          {
            name: 'primaryLocation',
            cp: 'checkBox',
            label: t('make_t_y_p_loc'),
            wrapStyle: 'responsive-margin-top',
            visibleOptionalLabel: false,
          },
          {
            name: 'latLon',
            cp: 'latLonPicker',
            wrapStyle: cnj(classes.latLanWrapper, 'responsive-margin-top'),
            center: [41.0536, 28.982],
            draggable: false,
            zoom: locationZoomLevelValues?.[values?.location?.category] || 10,
            className: rightSideProps?.isFromRightSide && classes.latLonPicker,
          },

          values?.share && {
            name: 'description',
            cp: 'richtext',
            label: t('tell_more_about_location'),
            wrapStyle: 'responsive-margin-top',
            maxLength: 2000,
          },
        ].filter(Boolean)
      }
      enableReinitialize
      rightSideProps={rightSideProps}
      {...rest}
    />
  );
}
