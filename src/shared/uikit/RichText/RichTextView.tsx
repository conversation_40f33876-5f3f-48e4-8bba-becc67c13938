'use client';

import type { MouseEvent, SyntheticEvent } from 'react';
import React, { useMemo, useRef, useState } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import removeRichTextBreakLines from 'shared/utils/toolkit/removeRichTextBreakLines';
import { getFirstBackgroundParent } from 'shared/utils/getFirstBackgroundParent';
import preventClickHandler from 'shared/utils/toolkit/preventClickHandler';
import ReactHtmlParser, { convertNodeToElement } from 'react-html-parser';
import HashtagPopper from 'shared/components/molecules/HashtagPopper';
import { EntitiyPopper } from '@shared/components/molecules/EntityPopper/EntitiyPopper';
import Flex from '../Flex';
import cnj from '../utils/cnj';
import Typography from '../Typography';
import BaseButton from '../Button/BaseButton';
import type { TypographyProps, TextProps } from '../Typography';
import useMedia from '../utils/useMedia';
import classes from './RichText.view.component.module.scss';

interface IRichTextView {
  typographyProps?: Omit<TypographyProps, 'children'>;
  html: string;
  onMentionClick?: (
    username: string,
    id: string,
    type: 'pages' | 'people'
  ) => void;
  onHashtagClick?: (hashtag: string, e?: MouseEvent<any>) => void;
  onHashtagHover?: (e: SyntheticEvent) => void;
  hoveredHashtag?: string;
  className?: string;
  showMore: boolean;
  row?: number;
  moreButtonVisible?: boolean;
  seeMoreClassName?: string;
  seeMoreTextProps?: TextProps<'span'>;
  onClick?: (evt?: MouseEvent<any>) => void;
  showHashtagPopper?: boolean;
}

const RichTextView = ({
  typographyProps = {},
  html = '',
  onMentionClick,
  onHashtagClick,
  onHashtagHover,
  showHashtagPopper = true,
  hoveredHashtag,
  className,
  showMore,
  row = 4,
  moreButtonVisible = true,
  seeMoreClassName,
  seeMoreTextProps = {},
  onClick,
}: IRichTextView): any => {
  const [seeAll, setSeeAll] = useState(false);
  const containerRef = useRef<HTMLElement>(null);
  const { t } = useTranslation();
  const { isTabletAndLess } = useMedia();

  const hasMore = useMemo(() => {
    if (!containerRef.current) return false;
    const { scrollHeight, clientHeight } = containerRef.current;
    return scrollHeight > clientHeight + 5;
  }, [containerRef.current]);
  const handleClick = (e?: MouseEvent<any>) => {
    if (typeof onClick === 'function') {
      onClick?.(e);
    }
    e?.stopPropagation();
    const target = e?.target as Element;
    const isMentionChild = target.getAttribute('contenteditable') === 'false';

    if (!isMentionChild) return;
    const element = target?.parentNode as Element;
    const denotation = element.getAttribute('data-denotation-char');
    const data = element.getAttribute('data-value');
    const objId = element.getAttribute('data-userid');
    const userType =
      element.getAttribute('data-user-type') === 'PAGE' ? 'page' : 'person';
    if (
      !!onMentionClick &&
      (denotation === '@' || (denotation === '' && !!objId))
    ) {
      onMentionClick(undefined, objId, userType);
      return;
    }
    if (!!onHashtagClick && denotation === '#' && !isTabletAndLess) {
      onHashtagClick?.(data?.toLowerCase() as any, e);
    }
  };

  const sanitizedHtml = html.replace(/contenteditable="[^"]*"/gi, '');
  const val = useMemo(() => removeRichTextBreakLines(sanitizedHtml), [html]);

  const handleSeeMore = (e?: MouseEvent<any>) => {
    e?.stopPropagation();
    e?.preventDefault();
    setSeeAll(true);
    if (e) preventClickHandler(e);
  };

  const transform = (node: any, index: any) => {
    if (
      showHashtagPopper &&
      node.type === 'tag' &&
      node.name === 'span' &&
      node.attribs['data-denotation-char'] === '#'
    ) {
      return (
        <HashtagPopper
          key={index}
          hoveredHashtag={hoveredHashtag || ''}
          onHashtagClick={onHashtagClick}
        >
          <span onMouseEnter={onHashtagHover} onMouseDown={onHashtagHover}>
            {convertNodeToElement(node, index, transform)}
          </span>
        </HashtagPopper>
      );
    }
    if (
      showHashtagPopper &&
      node.type === 'tag' &&
      node.name === 'span' &&
      node.attribs['data-denotation-char'] === ''
    ) {
      const userId = node.attribs?.['data-userid'];
      const isPage = node.attribs?.['data-user-type'] === 'PAGE';

      return (
        <EntitiyPopper
          key={index}
          username={userId}
          useId
          isPage={isPage}
          classNames={{ buttonWrapper: classes.buttonWrapper }}
        >
          {convertNodeToElement(node, index, transform)}
        </EntitiyPopper>
      );
    }
  };

  return (
    <Flex className={cnj(classes.mainContainer, className)}>
      <Typography height={21} {...typographyProps}>
        <Flex
          onClick={handleClick}
          ref={containerRef}
          as="span"
          className={cnj(
            'richTextViewContent',
            classes.richTextViewContainer,
            (!!onMentionClick || !!onHashtagClick) &&
              classes.richTextWithMentionHover,
            !!onMentionClick && classes.richTextWithMentionClick,
            classes.outerWrapper
          )}
          style={
            seeAll
              ? {}
              : {
                  WebkitLineClamp: row,
                  lineClamp: row,
                  display: seeAll || !showMore ? 'initial' : undefined,
                }
          }
        >
          {ReactHtmlParser(val, { transform })}
        </Flex>
      </Typography>
      {hasMore && showMore && !seeAll && moreButtonVisible && (
        <Flex flexDir="row" className={classes.seeMoreContainer}>
          <BaseButton
            className={cnj(
              classes.readMoreBtn,
              'read-more-btn',
              seeMoreClassName
            )}
            onClick={handleSeeMore}
            ref={(refs) => {
              if (!refs) return;
              const bg = getFirstBackgroundParent(refs);
              refs.style.backgroundColor = bg;
            }}
          >
            <Typography
              color="brand"
              {...seeMoreTextProps}
              className={cnj(classes.readMoreText, seeMoreTextProps.className)}
            >
              {`... ${t('see_more')}`}
            </Typography>
          </BaseButton>
        </Flex>
      )}
    </Flex>
  );
};

export default RichTextView;
