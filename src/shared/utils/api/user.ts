import Cookies from 'shared/utils/toolkit/cookies';
import { decodeObject } from 'shared/utils/middleware-utils';
import isClient from 'shared/utils/toolkit/isClient';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';
import type { UserApiResponse, UserType } from 'shared/types/user';
import request from '../toolkit/request';
import {
  authEndpoints,
  profileEndpoints,
} from '../constants/servicesEndpoints';
import Endpoints from '../constants/endpoints';
import beforeCacheUserInfo from '../normalizers/beforeCacheUserInfo';

const getUserNameByObjectId = async ({
  objectId,
  accessToken,
}: {
  objectId: string;
  accessToken?: string;
}): Promise<any> => {
  const { data } = await request.get(authEndpoints.usernameDetector, {
    params: {
      accessToken,
      userId: objectId,
    },
  });
  return data;
};

const getAuthUser = async ({ accessToken }: { accessToken?: string }) => {
  let token = accessToken;
  if (!accessToken && isClient()) {
    const USER_OBJ_TOKEN = getCookieKey('userObjToken');
    const userObjToken = decodeObject<any>(Cookies.get(USER_OBJ_TOKEN));
    token = userObjToken?.accessToken;
  }

  const { data } = await request.get<UserApiResponse>(
    Endpoints.Auth.getAuthUserInfo,
    {
      accessToken: token,
    }
  );
  return beforeCacheUserInfo(data);
};

const getUser = async ({
  params,
  accessToken,
}: {
  params: any;
  accessToken?: string;
}) => {
  if (!params?.username && !params?.userId) {
    throw new Error('username or userId is required');
  }

  const { isLightVersion, ...rest } = params;

  const url = isLightVersion
    ? Endpoints.App.User.Profile.getUserProfileByUserPublicIDLight
    : Endpoints.App.User.Profile.getUserProfileByUserPublicID;

  const { data } = await request.get<UserApiResponse>(url, {
    params: { ...rest },
    accessToken,
  });

  return beforeCacheUserInfo(data);
};
const deleteAuthUser = async (): Promise<UserType> => {
  const { data } = await request.delete(Endpoints.Auth.getAuthUserInfo);
  return data;
};

const verifyUserDelete = async ({ token }: any): Promise<any> => {
  const { data } = await request.delete(authEndpoints.verifyUserDelete(token));
  return data;
};

const checkProfile = async (): Promise<any> => {
  const { data } = await request.get(profileEndpoints.checkProfile);
  return data?.value;
};

export {
  getAuthUser,
  getUser,
  getUserNameByObjectId,
  deleteAuthUser,
  verifyUserDelete,
  checkProfile,
};
