import { QueryKeys } from 'shared/utils/constants/enums';
import { getAuthUser } from 'shared/utils/api/user';
import { useAuthState } from 'shared/contexts/Auth/auth.provider';
import type { UseQueryResult } from '@tanstack/react-query';
import type { UserType } from 'shared/types/user';
import Cookies from 'shared/utils/toolkit/cookies';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';
import {
  authUserNormalizer,
  encodeObject,
} from 'shared/utils/middleware-utils';
import useIsMounted from 'shared/hooks/useIsMounted';
import useReactQuery from './useReactQuery';

export type UseAuthUserReturnType = ReturnType<typeof useAuthUser>;

const useAuthUser = () => {
  const isLoggedIn = useAuthState('isLoggedIn');
  const isMounted = useIsMounted();

  return useReactQuery<UserType>({
    action: {
      key: [QueryKeys.authUser],
      apiFunc: getAuthUser,
    },
    config: {
      enabled: isLoggedIn && !isMounted,
      // staleTime: 2 * 1000,
      // refetchOnMount: true,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,

      onSuccess: (res) => {
        const AUTH_USER = getCookieKey('authUser');
        Cookies.set(AUTH_USER, encodeObject(authUserNormalizer(res)));
      },
    },
  });
};

export default useAuthUser;
