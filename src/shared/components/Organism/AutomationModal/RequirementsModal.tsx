import React, { useState } from 'react';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import Flex from '@shared/uikit/Flex';
import AutoComplete from '@shared/uikit/AutoComplete';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Typography from '@shared/uikit/Typography';
import Icon from '@shared/uikit/Icon';
import useMedia from '@shared/uikit/utils/useMedia';
import Button from '@shared/uikit/Button';
import IconButton from '@shared/uikit/Button/IconButton';
import TextInput from '@shared/uikit/TextInput';
import Box from '@shared/uikit/Layout/Box';
import geoApi from 'shared/utils/api/geo';
import Form from '@shared/uikit/Form';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import { setAutoMovement } from '@shared/utils/api/jobs';
import useToast from '@shared/uikit/Toast/useToast';
import { useMutation } from '@tanstack/react-query';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import { useParams } from 'next/navigation';
import classes from './RequirementsModal.module.scss';

interface ActionOption {
  value: string;
  label: string;
}

interface TypeOption {
  value: string;
  label: string;
}

interface RequirementBox {
  id: string;
  action: ActionOption | null;
  type: TypeOption | null;
  value: any;
}

interface StageOption {
  value: string;
  label: string;
  icon: ReactNode;
}

interface FormValues {
  [key: string]: any;
}

interface AutoMovementData {
  toPipelineId: number;
  coverLetterCheckingEnabled: boolean;
  phoneNumberCheckingEnabled: boolean;
  ageRangeCheckingEnabled: boolean;
  ageRangeIsBetweenOrNot: boolean;
  minAge: number;
  maxAge: number;
  locationCheckingEnabled: boolean;
  locationCountryCodeIsEqualsOrNot: boolean;
  locationCountryCodeIs: string;
}

const RequirementsModal: React.FC = () => {
  const { pipelineId } = useParams();
  const { t } = useTranslation();
  const dispatch = useGlobalDispatch();
  const { isMoreThanTablet } = useMedia();
  const toast = useToast();

  const [selectedStage, setSelectedStage] = useState<StageOption>({
    value: 'review',
    label: t('Review'),
    icon: (
      <IconButton
        name="circle-s"
        type="far"
        size="sm18"
        variant="rectangle"
        iconProps={{ color: 'trench' }}
        className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
      />
    ),
  });
  const [requirementBoxes, setRequirementBoxes] = useState<RequirementBox[]>(
    []
  );
  const stageOptions: StageOption[] = [
    {
      value: 'review',
      label: t('Review'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          variant="rectangle"
          iconProps={{ color: 'trench' }}
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'interview',
      label: t('Interview'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'error' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'hiring',
      label: t('Hiring'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'pendingOrange' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'offered',
      label: t('Offered'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'success' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
  ];
  const LOCATION_FIELD = {
    apiFunc: geoApi.searchPlace,
    cp: 'asyncAutoCompleteWithExtraParams',
    label: t('location'),
    name: 'locationWithExtraParams',
    autoComplete: 'nope',

    rightIconProps: {
      name: 'search',
    },
    visibleRightIcon: true,
  };
  const actionOptions: ActionOption[] = [
    { value: 'location', label: t('location') },
    { value: 'age', label: t('age') },
    { value: 'coverLetter', label: t('cover_letter') },
    { value: 'phoneNumber', label: t('phone_number') },
  ];

  const typeOptions: TypeOption[] = [
    { value: 'is', label: t('is') },
    { value: 'isNot', label: t('is_not') },
  ];

  const { mutateAsync: submitAutoMovement } = useMutation({
    mutationFn: setAutoMovement,
    onSuccess: () => {
      toast({
        type: 'success',
        icon: 'check-circle',
        title: t('success'),
        message: t('auto_movement_updated'),
      });
      handleClose();
    },
    onError: (error: any) => {
      toast({
        type: 'error',
        icon: 'times-circle',
        title: t('error'),
        message: error.response?.data?.defaultMessage || t('error_occurred'),
      });
    },
  });

  const handleClose = () => {
    dispatch({ type: 'TOGGLE_REQUIREMENTS_MODAL', payload: { open: false } });
  };

  const handleAddBox = () => {
    setRequirementBoxes([
      ...requirementBoxes,
      {
        id: Date.now().toString(),
        action: null,
        type: null,
        value: null,
      },
    ]);
  };

  const handleRemoveBox = (id: string) => {
    setRequirementBoxes(requirementBoxes.filter((box) => box.id !== id));
  };

  const handleActionChange = (id: string, action: ActionOption) => {
    setRequirementBoxes(
      requirementBoxes.map((box) =>
        box.id === id ? { ...box, action, type: null, value: null } : box
      )
    );
  };

  const handleTypeChange = (id: string, type: TypeOption) => {
    setRequirementBoxes(
      requirementBoxes.map((box) =>
        box.id === id ? { ...box, type, value: null } : box
      )
    );
  };

  const renderValueField = (box: RequirementBox) => {
    if (!box.action || !box.type) return null;

    switch (box.action.value) {
      case 'location':
        return <DynamicFormBuilder groups={[LOCATION_FIELD]} />;
      case 'age':
        return (
          <Flex flexDir="row" className="gap-[12px] w-full justify-between">
            <TextInput
              inputWrapClassName={classes.inputConditionWrap}
              type="number"
              placeholder={t('min_age')}
            />
            <TextInput
              inputWrapClassName={classes.inputConditionWrap}
              type="number"
              placeholder={t('max_age')}
            />
          </Flex>
        );
      case 'coverLetter':
        return <TextInput placeholder={t('enter_cover_letter_text')} />;
      case 'phoneNumber':
        return <TextInput type="tel" placeholder={t('enter_phone_number')} />;
      default:
        return null;
    }
  };

  const handleFormSubmit = (values: FormValues): Promise<any> => {
    const formData: AutoMovementData = {
      toPipelineId: parseInt(selectedStage.value, 10),
      coverLetterCheckingEnabled: requirementBoxes.some(
        (box) => box.action?.value === 'coverLetter'
      ),
      phoneNumberCheckingEnabled: requirementBoxes.some(
        (box) => box.action?.value === 'phoneNumber'
      ),
      ageRangeCheckingEnabled: requirementBoxes.some(
        (box) => box.action?.value === 'age'
      ),
      ageRangeIsBetweenOrNot:
        requirementBoxes.find((box) => box.action?.value === 'age')?.type
          ?.value === 'is',
      minAge:
        requirementBoxes.find((box) => box.action?.value === 'age')?.value
          ?.minAge || 0,
      maxAge:
        requirementBoxes.find((box) => box.action?.value === 'age')?.value
          ?.maxAge || 0,
      locationCheckingEnabled: requirementBoxes.some(
        (box) => box.action?.value === 'location'
      ),
      locationCountryCodeIsEqualsOrNot:
        requirementBoxes.find((box) => box.action?.value === 'location')?.type
          ?.value === 'is',
      locationCountryCodeIs:
        requirementBoxes.find((box) => box.action?.value === 'location')?.value
          ?.countryCode || '',
    };

    return submitAutoMovement({
      pipelineId: pipelineId as string,
      data: formData,
    });
  };

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
    >
      <ModalHeaderSimple title={t('requirements')} />
      <ModalBody className={classes.modalBody}>
        <Flex className={classes.stageSelectContainer}>
          <Typography size={16}>{t('move_to')}</Typography>
          <Icon name="Pipe-move" type="far" size={20} />
        </Flex>
        <Form<FormValues, AutoMovementData>
          initialValues={{}}
          apiFunc={handleFormSubmit}
          onSuccess={() => {
            toast({
              type: 'success',
              icon: 'check-circle',
              title: t('success'),
              message: t('auto_movement_updated'),
            });
            handleClose();
          }}
          onFailure={(error: any) => {
            toast({
              type: 'error',
              icon: 'times-circle',
              title: t('error'),
              message:
                error.response?.data?.defaultMessage || t('error_occurred'),
            });
          }}
        >
          <Flex className={classes.content}>
            <AutoComplete
              editable={false}
              visibleRightIcon
              variant="simple"
              value={selectedStage}
              onChangeInput={(value: any) => {
                const option = stageOptions.find(
                  (opt) => opt.value === value.value
                );
                if (option) {
                  setSelectedStage(option);
                }
              }}
              leftIcon={
                <Flex flexDir="row" alignItems="center" className="p-4">
                  {selectedStage.icon}
                </Flex>
              }
              inputWrapClassName={classes.inputWrap}
              options={stageOptions}
              rightIconClassName={classes.rightIcon}
              renderItem={({ item }) => (
                <Flex
                  flexDir="row"
                  alignItems="center"
                  className="w-full h-[40px] gap-10"
                >
                  {item.icon}
                  <Typography size={16}>{item.label}</Typography>
                </Flex>
              )}
              className={classes.stageSelect}
              optionsVariant={isMoreThanTablet ? 'dropdown' : 'bottomsheet'}
              displayName={selectedStage.label}
              onSelect={(item: any) => {
                setSelectedStage(item);
              }}
            />
            {requirementBoxes.map((box) => (
              <Box key={box.id} className={classes.requirementBox}>
                <Flex
                  flexDir="row"
                  className="justify-between mb-2 w-full "
                  alignItems="center"
                >
                  <Typography size={20} fontWeight={700}>
                    {t('if')}
                  </Typography>
                  <Icon
                    name="times"
                    type="far"
                    color="smoke_coal"
                    size={20}
                    onClick={() => handleRemoveBox(box.id)}
                    className="cursor-pointer opacity-20"
                  />
                </Flex>
                <Flex flexDir="column" className="gap-[12px]">
                  <AutoComplete
                    editable={false}
                    visibleRightIcon
                    variant="simple"
                    placeholder={t('action')}
                    value={box.action ?? { value: '', label: '' }}
                    onSelect={(value: any) => handleActionChange(box.id, value)}
                    inputWrapClassName={classes.inputConditionWrap}
                    options={actionOptions}
                    rightIconClassName={classes.rightIcon}
                    className={classes.stageSelect}
                    optionsVariant={
                      isMoreThanTablet ? 'dropdown' : 'bottomsheet'
                    }
                  />
                  {box.action && (
                    <AutoComplete
                      editable={false}
                      visibleRightIcon
                      variant="simple"
                      placeholder={t('type')}
                      value={box.type ?? { label: '', value: '' }}
                      onSelect={(value: any) => handleTypeChange(box.id, value)}
                      inputWrapClassName={classes.inputConditionWrap}
                      options={typeOptions}
                      rightIconClassName={classes.rightIcon}
                      className={classes.stageSelect}
                      optionsVariant={
                        isMoreThanTablet ? 'dropdown' : 'bottomsheet'
                      }
                    />
                  )}
                  {box.action && box.type && renderValueField(box)}
                </Flex>
              </Box>
            ))}

            <Flex flexDir="row" className="justify-start mb-4">
              <Button
                variant="default"
                shape="capsule"
                onClick={handleAddBox}
                className={classes.addBoxButton}
              >
                <Icon name="plus" type="far" size={20} color="white" />
              </Button>
            </Flex>

            <Box className="border border-solid border-t border-techGray_20 h-[72px] absolute bottom-0 left-0 right-0 p-[20px] ">
              <Flex
                flexDir="row"
                className="justify-between gap-4 items-center size-full"
              >
                <Button
                  className={classes.discardButton}
                  variant="default"
                  fullWidth
                  onClick={handleClose}
                >
                  {t('discard')}
                </Button>
                <SubmitButton
                  variant="default"
                  className={classes.nextButton}
                  fullWidth
                >
                  {t('next')}
                </SubmitButton>
              </Flex>
            </Box>
          </Flex>
        </Form>
      </ModalBody>
    </FixedRightSideModal>
  );
};

export default RequirementsModal;
