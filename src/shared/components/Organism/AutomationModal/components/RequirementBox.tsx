import React from 'react';
import Flex from '@shared/uikit/Flex';
import AutoComplete from '@shared/uikit/AutoComplete';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Typography from '@shared/uikit/Typography';
import Icon from '@shared/uikit/Icon';
import useMedia from '@shared/uikit/utils/useMedia';
import TextInput from '@shared/uikit/TextInput';
import Box from '@shared/uikit/Layout/Box';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import geoApi from 'shared/utils/api/geo';
import classes from '../RequirementsModal.module.scss';
import type { ActionOption, TypeOption } from './types';

interface RequirementBoxProps {
  id: string;
  action: ActionOption | null;
  type: TypeOption | null;
  value: any;
  onRemove: (id: string) => void;
  onActionChange: (id: string, action: ActionOption) => void;
  onTypeChange: (id: string, type: TypeOption) => void;
  onValueChange: (id: string, field: string, value: any) => void;
  actionOptions: ActionOption[];
  typeOptions: TypeOption[];
}

export const RequirementBox: React.FC<RequirementBoxProps> = ({
  id,
  action,
  type,
  value,
  onRemove,
  onActionChange,
  onTypeChange,
  onValueChange,
  actionOptions,
  typeOptions,
}) => {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();

  const LOCATION_FIELD = {
    apiFunc: geoApi.searchPlace,
    cp: 'asyncAutoCompleteWithExtraParams',
    label: t('location'),
    name: 'locationWithExtraParams',
    autoComplete: 'nope',
    rightIconProps: {
      name: 'search',
    },
    visibleRightIcon: true,
  };

  const renderValueField = () => {
    if (!action || !type) return null;

    switch (action.value) {
      case 'location':
        return (
          <DynamicFormBuilder
            groups={[
              {
                ...LOCATION_FIELD,
                onChange: (locationValue: any) => {
                  if (locationValue && locationValue.countryCode) {
                    onValueChange(id, 'countryCode', locationValue.countryCode);
                  }
                },
                value: value?.countryCode
                  ? {
                      countryCode: value.countryCode,
                      label: value.countryCode,
                      value: value.countryCode,
                    }
                  : undefined,
              },
            ]}
          />
        );
      case 'age':
        return (
          <Flex flexDir="row" className="gap-[12px] w-full justify-between">
            <TextInput
              inputWrapClassName={classes.inputConditionWrap}
              placeholder={t('min_age')}
              value={value?.minAge?.toString() || ''}
              onChange={(e) => {
                const inputValue = typeof e === 'string' ? e : e.target.value;
                onValueChange(id, 'minAge', parseInt(inputValue, 10) || 0);
              }}
            />
            <TextInput
              inputWrapClassName={classes.inputConditionWrap}
              placeholder={t('max_age')}
              value={value?.maxAge?.toString() || ''}
              onChange={(e) => {
                const inputValue = typeof e === 'string' ? e : e.target.value;
                onValueChange(id, 'maxAge', parseInt(inputValue, 10) || 0);
              }}
            />
          </Flex>
        );
      case 'coverLetter':
        return (
          <TextInput
            placeholder={t('enter_cover_letter_text')}
            value={value?.text || ''}
            onChange={(e) => {
              const inputValue = typeof e === 'string' ? e : e.target.value;
              onValueChange(id, 'text', inputValue);
            }}
          />
        );
      case 'phoneNumber':
        return (
          <TextInput
            type="tel"
            placeholder={t('enter_phone_number')}
            value={value?.phoneNumber || ''}
            onChange={(e) => {
              const inputValue = typeof e === 'string' ? e : e.target.value;
              onValueChange(id, 'phoneNumber', inputValue);
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box className={classes.requirementBox}>
      <Flex
        flexDir="row"
        className="justify-between mb-2 w-full"
        alignItems="center"
      >
        <Typography size={20} fontWeight={700}>
          {t('if')}
        </Typography>
        <Icon
          name="times"
          type="far"
          color="smoke_coal"
          size={20}
          onClick={() => onRemove(id)}
          className="cursor-pointer opacity-20"
        />
      </Flex>
      <Flex flexDir="column" className="gap-[12px]">
        <AutoComplete
          key={`action_${id}`}
          editable={false}
          visibleRightIcon
          variant="simple"
          placeholder={t('action')}
          value={action ?? { value: '', label: '' }}
          onSelect={(selectedValue: any) => onActionChange(id, selectedValue)}
          inputWrapClassName={classes.inputConditionWrap}
          options={actionOptions}
          rightIconClassName={classes.rightIcon}
          className={classes.stageSelect}
          optionsVariant={isMoreThanTablet ? 'dropdown' : 'bottomsheet'}
          displayName={action?.label || ''}
        />
        {action && (
          <AutoComplete
            key={`type_${id}`}
            editable={false}
            visibleRightIcon
            variant="simple"
            placeholder={t('type')}
            value={type ?? { label: '', value: '' }}
            onSelect={(selectedTypeValue: any) =>
              onTypeChange(id, selectedTypeValue)
            }
            inputWrapClassName={classes.inputConditionWrap}
            options={typeOptions}
            rightIconClassName={classes.rightIcon}
            className={classes.stageSelect}
            optionsVariant={isMoreThanTablet ? 'dropdown' : 'bottomsheet'}
            displayName={type?.label || ''}
          />
        )}
        {action && type && <div key={`value_${id}`}>{renderValueField()}</div>}
      </Flex>
    </Box>
  );
};

export default RequirementBox;
