import { useEffect, useMemo } from 'react';
import { type FormikProps } from 'formik';
import last from 'lodash/last';
import dayjs from 'dayjs';
import { unformat } from '@react-input/mask';
import { useQueryClient } from '@tanstack/react-query';
import geoNormalizer from '@shared/utils/normalizers/geo';
import { type OptionType } from '@shared/uikit/PhoneInputSimple/CountryPhoneInput';
import geo from '@shared/utils/constants/servicesEndpoints/services/geo';
import formValidator from 'shared/utils/form/formValidator';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { Endpoints, QueryKeys } from '@shared/utils/constants';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Typography from '@shared/uikit/Typography';
import useBusinessPage from '@shared/utils/hooks/useBusinessPage';
import { proceedPayment } from '@shared/utils/api/payment';
import type { DynamicComponentBuilderItemProps } from '@shared/uikit/Form/ComponentBuilder';
import PlanInfoCard from '@shared/components/molecules/PlanCad/PlanCard.infoCard';
import { StyledLogo } from '@shared/svg/LogoIcon';
import PlanCardPrice from '@shared/components/molecules/PlanCad/PlanCard.price';
import { setMultiStepFormData } from '@shared/hooks/useMultiStepForm';
import { type ILocation } from '@shared/types/lookup';
import useBraintree from './useBraintree';
import type { CheckoutChildModalData } from '.';
import classes from './index.module.scss';

interface ChargePayloadType {
  savedCard: boolean;
  cardId: string;
  paypalEmail: null;
  name?: string;
  amount: string | number;
  cardToken?: string;
  shippingAddress: {
    addressLine1?: string;
    addressLine2?: string;
    adminArea1?: string;
    adminArea2?: string;
    postalCode?: string;
    countryCode?: string;
  };
  deviceData?: string;
  customerBrowser?: string;
  requestId?: string;
  requestType: string;
  taxAmount?: string | number;
  pageId?: string;
}

export interface PaymentFormFields {
  savedCard: boolean;
  cardId?: OptionType;
  paypalEmail: string;
  seats?: string;
  cardNumber: string;
  name?: string;
  expirationDate: string;
  securityCode: string;
  addressLine1?: string;
  addressLine2?: string;
  countryCode?: OptionType;
  adminArea1?: ILocation;
  adminArea2?: ILocation;
  postalCode?: string;
  taxId?: string;
}

const useCheckoutForm = ({
  entityData,
  invoice,
  actions,
  modalProps = {},
}: CheckoutChildModalData) => {
  const creditCardMaskOptions = {
    mask: '**** - **** - **** - ****',
    replacement: { '*': /\d/ },
    separate: true,
  };
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const isWideModal = useMemo(
    () => invoice?.requestType && ['PUBLISH_JOB'].includes(invoice.requestType),
    [invoice?.requestType]
  );
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
    isNarrow: !isWideModal,
  });
  // useEffect(() => {
  //   setMultiStepFormData('checkout', {
  //     options: {
  //       isSuccess: true,
  //       title: t('payment_successful'),
  //       message:
  //         entityData.requestType === 'NEW_PLAN'
  //           ? t('thanks_for_purchasing_item')
  //           : t('thanks_for_your_purchase'),
  //       status: 'failure',
  //       billingId: 102,
  //     },
  //     data: {
  //       modalProps: {
  //         ...modalProps,
  //         wide: isWideModal,
  //       },
  //       entityData,
  //       invoice,
  //       actions,
  //     },
  //   });
  // }, []);

  const { data: businessPageData } = useBusinessPage({ isEnabled: true });

  const { tokenize, dataCollector, paymentMethods } = useBraintree();

  return {
    title: t('checkout'),
    businessPageData,
    modalProps: {
      ...modalProps,
      wide: isWideModal,
    },
    groups: (
      props: FormikProps<PaymentFormFields>
    ): DynamicComponentBuilderItemProps[] =>
      [
        {
          // eslint-disable-next-line react/no-unstable-nested-components
          cp: () => (
            <>
              <Typography size={16} height={19} font="500" mb={12}>
                {t('selected_job')}
              </Typography>
              <PlanInfoCard
                Icon={StyledLogo}
                label={entityData?.label || ''}
                cardProps={{
                  subTitle: entityData?.subTitle,
                  valueProps: { color: 'secondaryDisabledText' },
                  className: classes.planInfoCard,
                }}
              >
                <PlanCardPrice
                  price={invoice?.price}
                  priceProps={{ size: 16, height: 19, font: '600' }}
                  priceUnit={entityData?.priceUnit}
                  priceUnitProps={{
                    size: 14,
                    height: 16,
                    isWordWrap: false,
                    isTruncated: false,
                  }}
                  wrapperClassName={classes.planCardPriceWrapper}
                />
              </PlanInfoCard>
            </>
          ),
          hide: invoice?.requestType !== 'PUBLISH_JOB',
        },
        {
          cp: () => (
            <>
              <Typography size={16} height={19} font="500" mb={12}>
                {t('selected_plan')}
              </Typography>
              <PlanInfoCard
                Icon={entityData?.Logo || StyledLogo}
                label={entityData?.label || ''}
                cardProps={{
                  subTitle: entityData?.subTitle,
                  valueProps: { color: 'secondaryDisabledText' },
                  className: classes.planInfoCard,
                }}
              >
                <PlanCardPrice
                  price={invoice?.price}
                  priceProps={{ size: 16, height: 19, font: '600' }}
                  priceUnit={entityData?.priceUnit}
                  priceUnitProps={{
                    size: 14,
                    height: 16,
                    isWordWrap: false,
                    isTruncated: false,
                  }}
                  wrapperClassName={classes.planCardPriceWrapper}
                />
              </PlanInfoCard>
            </>
          ),
          hide: invoice?.requestType !== 'NEW_PLAN',
        },
        // {
        //   cp: () => (
        //     <Typography size={16} height={19} font="500" mt={20} mb={12}>
        //       {t('seats')}
        //     </Typography>
        //   ),
        //   hide: invoice?.requestType !== 'NEW_PLAN',
        // },
        // {
        //   name: 'seats',
        //   cp: 'numberInput',
        //   inputStyle: classes.numberOfSeatsInput,
        //   withRightIconClassName: 'my-auto',
        //   wrapStyle: 'responsive',
        //   required: true,
        //   hide: invoice?.requestType !== 'NEW_PLAN',
        // },
        {
          cp: () => (
            <Typography size={16} height={19} font="500" mt={20} mb={12}>
              {t('enter_your_payment_details')}
            </Typography>
          ),
        },
        {
          name: 'cardId',
          cp: 'dropdownSelect',
          label: t('saved_cards'),
          options: paymentMethods,
          // onClear: () => {
          //   props?.setFieldValue?.('cardId', undefined);
          // },
          clearable: true,
          wrapStyle: 'responsive',
          cancellable: true,
        },
        {
          name: 'cardNumber',
          cp: 'maskedInput',
          label: t('card_number'),
          wrapStyle: 'responsive-margin-top',
          required: !props?.values?.cardId?.value,
          disabled: props?.values?.cardId?.value,
          ...creditCardMaskOptions,
        },
        {
          name: 'name',
          cp: 'input',
          label: t('name_on_payment_method'),
          wrapStyle: 'responsive-margin-top',
          disabled: props?.values?.cardId?.value,
        },
        {
          name: 'expirationDate',
          cp: 'maskedInput',
          label: 'MM/YY',
          wrapStyle: classes.halfWidthInput,
          isFirstHalfWidth: true,
          required: !props?.values?.cardId?.value,
          mask: 'MM/YY',
          replacement: { M: /\d/, Y: /\d/ },
          disabled: props?.values?.cardId?.value,
        },
        {
          name: 'securityCode',
          cp: 'input',
          label: t('security_code'),
          isSecondHalfWidth: true,
          wrapStyle: classes.halfWidthInput,
          required: !props?.values?.cardId?.value,
          disabled: props?.values?.cardId?.value,
        },
        {
          name: 'addressLine1',
          cp: 'input',
          label: t('billing_address'),
          wrapStyle: 'responsive-margin-top',
          disabled: props?.values?.cardId?.value,
        },
        {
          name: 'countryCode',
          label: t('county'),
          required: !props?.values?.cardId?.value,
          forceVisibleError: true,
          cp: 'asyncAutoComplete',
          initSearchValue: businessPageData?.location?.countryCode,
          url: Endpoints.App.Common.getCountries,
          isFirstHalfWidth: true,
          wrapStyle: classes.halfWidthInput,
          normalizer: geoNormalizer.updatedSearchCountry,
          disabled: props?.values?.cardId?.value,
        },
        {
          name: 'adminArea1',
          label: t('city_town_village'),
          required: !props?.values?.cardId?.value,
          forceVisibleError: true,
          cp: 'asyncAutoComplete',
          url: geo.location.suggestCity,
          params: {
            countryCode: props.values?.countryCode?.value,
          },
          disabled:
            props?.values?.cardId?.value || !props.values?.countryCode?.value,
          isSecondHalfWidth: true,
          wrapStyle: classes.halfWidthInput,
          normalizer: geoNormalizer.updatedSearchCity,
        },
        {
          name: 'adminArea2',
          label: t('province_region'),
          textInputProps: {
            labelProps: {
              isTruncated: true,
            },
          },
          cp: 'asyncAutoComplete',
          url: geo.location.suggestPlace,
          params: {
            countryCode: props.values?.countryCode?.value,
          },
          isFirstHalfWidth: true,
          wrapStyle: classes.halfWidthInput,
          normalizer: geoNormalizer.updatedSearchCity,
          disabled: props?.values?.cardId?.value,
        },
        {
          name: 'postalCode',
          cp: 'input',
          label: t('postal_code'),
          isSecondHalfWidth: true,
          wrapStyle: classes.halfWidthInput,
          disabled: props?.values?.cardId?.value,
        },
        {
          name: 'taxId',
          cp: 'input',
          label: t('vat_ID'),
          wrapStyle: 'responsive-margin-top',
        },
      ].filter(({ hide }) => !hide),
    onSuccess: ({
      invoiceId,
      message,
      success,
    }: {
      invoiceId: string;
      message?: string | null;
      success: boolean;
    }) => {
      queryClient.refetchQueries([QueryKeys.getTicketsList]);
      setMultiStepFormData('checkout', {
        options: {
          isSuccess: true,
          title: t('payment_successful'),
          message:
            entityData.requestType === 'NEW_PLAN'
              ? t('thanks_for_purchasing_plan')
              : t('thanks_for_your_purchase'),
          status: success && !message ? 'success' : 'failure',
        },
      });
      actions?.onSuccess?.({
        invoiceId,
        message,
        success,
      });
    },
    onCancel: (isDirty: boolean) => () => {
      if (!isDirty) actions?.onCancel?.();
      else
        openConfirmDialog({
          title: t('confirm_title'),
          message: t('confirm_desc'),
          cancelButtonText: t('confirm_cancel'),
          confirmButtonText: t('confirm_ok'),
          cancelCallback: () => actions?.onCancel?.(),
          isReverse: true,
        });
    },
    onFailure: (error: any) => {
      // Todo: use Exception type to determine the error message.
      setMultiStepFormData('checkout', {
        options: {
          isSuccess: false,
          title: t('payment_not_successful'),
          message: t('payment_error_message'),
          status: 'error',
        },
      });
    },
    initialData: useMemo<PaymentFormFields>(
      () => ({
        savedCard: false,
        cardId: undefined,
        paypalEmail: '',
        seats: String(invoice?.quantity || '1'),
        name: '',
        amount: 0,
        cardNumber: '',
        expirationDate: '',
        securityCode: '',
        taxId: '',
        addressLine1: '',
        addressLine2: '',
        countryCode: businessPageData?.location?.countryCode
          ? {
              value: businessPageData.location.countryCode,
              label: last(
                businessPageData?.location?.label?.split(',')
              )?.trim()!,
            }
          : undefined,
        adminArea1: businessPageData?.location,
        adminArea2: undefined,
        postalCode: '',
        deviceData: '',
        customerBrowser: '',
        requestId: 0,
        taxAmount: 0,
        pageId: 0,
      }),
      [businessPageData?.location]
    ),
    apiFunc: proceedPayment,
    validationSchema: formValidator.object().shape({
      securityCode: formValidator
        .string()
        .min(3, t('security_code_validation_error')),
      expirationDate: formValidator
        .string()
        .test(
          'validExpirationDate',
          t('expiration_date_format_error'),
          (value) => !value || dayjs(value, 'MM/YY', true)?.isValid()
        ),
    }),
    transform: async (data: PaymentFormFields) => {
      if (!invoice) throw new Error('failed');
      const {
        cardNumber = '',
        securityCode,
        expirationDate,
        addressLine1,
        addressLine2,
        adminArea1,
        adminArea2,
        postalCode,
        countryCode,
        seats,
        cardId,
        name,
      } = data;

      const pageId = businessPageData?.id;
      const { price, requestType, id: requestId, taxAmount } = invoice;
      const payload: ChargePayloadType = {
        savedCard: false,
        cardId: '0',
        paypalEmail: null,
        cardToken: undefined,
        name,
        amount: Number(price),
        shippingAddress: {
          addressLine1,
          addressLine2,
          postalCode,
          adminArea1: adminArea1?.value,
          adminArea2: adminArea2?.value,
          countryCode: countryCode?.value,
        },
        deviceData: dataCollector?.deviceData,
        requestType,
        taxAmount,
        requestId,
        pageId,
      };

      if (cardId?.value) {
        payload.savedCard = true;
        payload.cardId = cardId.value;
      } else {
        const creditCard = {
          number: unformat(cardNumber, creditCardMaskOptions),
          cvv: securityCode,
          expirationDate,
          billingAddress: {
            addressLine1,
            addressLine2,
            postalCode,
            adminArea1: adminArea1?.value,
            adminArea2: adminArea2?.value,
            countryCode: countryCode?.value,
          },
        };
        const { error, data: tokenizedData } = await tokenize({
          creditCard,
          options: { validate: true },
        });
        if (error) throw error;
        // eslint-disable-next-line @typescript-eslint/no-shadow
        payload.cardToken = tokenizedData?.creditCards?.find(
          ({ nonce }) => nonce
        )?.nonce;
      }
      return payload;
    },
  };
};

export default useCheckoutForm;
