'use client';

import React, { useState } from 'react';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import type { MultiStepFormProps } from '../MultiStepForm';
import MultiStepForm from '../MultiStepForm';
import { useSubmitVendorStepOne } from './useSubmitVendorStepOne';
import { useSubmitVendorStepTwo } from './useSubmitVendorStepTwo';

type Props = {};
export type Method = 'google' | 'emails' | 'bulk' | undefined;

const SubmitVendor: React.FC<Props> = () => {
  const { data: submitVendorData } = useMultiStepFormState('submitToVendor');
  const initialStep = submitVendorData?.step === '1' ? 0 : 1;

  const [checkedIds, setCheckedIds] = useState<string[]>([]);

  const data = useSubmitVendorStepOne({
    checkedIds,
    setCheckedIds,
  });
  const sendStep = useSubmitVendorStepTwo({ checkedIds });

  const steps = [...data, ...sendStep];

  const onClose = () => closeMultiStepForm('submitToVendor');

  const getHeaderProps = getStepData('getHeaderProps', steps);
  const getStepHeaderProps = getStepData('getStepHeaderProps', steps);
  const renderBody = getStepData('renderBody', steps);
  const renderFooter = getStepData('renderFooter', steps);

  return (
    <MultiStepForm
      wide
      initialStep={initialStep}
      showConfirm={false}
      enableReinitialize
      totalSteps={steps?.length}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      isOpenAnimation
      formName="submitToVendor"
    />
  );
};

export default SubmitVendor;

function getStepData<T extends keyof MultiStepFormProps>(
  key: T,
  data: MultiStepFormProps[]
): MultiStepFormProps[T] {
  return ({ step, ...rest }: any) => data?.[step]?.[key]?.({ step, ...rest });
}
