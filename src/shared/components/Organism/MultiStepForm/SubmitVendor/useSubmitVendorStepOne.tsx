import { type Dispatch, type SetStateAction } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import eventKeys from 'shared/constants/event-keys';
import useBackToModal from '@shared/hooks/useBackToModal';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import type { SuggestSubmitToVendor } from '@shared/types/submitVendor';
import {
  getSuggestCompany,
  getVendorsExcluded,
  getVendorsIncluded,
} from '@shared/utils/api/company';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import MenuItem from '@shared/uikit/MenuItem';
import CheckBox from '@shared/uikit/CheckBox';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import ItemComponent from '../../AsyncPickerModal/components/ItemComponent';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '../MultiStepForm';
import SubmitVendorItemSkeleton from './SubmitVendorItemSkeleton';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type Args = {
  checkedIds: string[];
  setCheckedIds: Dispatch<SetStateAction<string[]>>;
};
const MAXIMUM_SUBMISSION = 50;

export function useSubmitVendorStepOne({
  checkedIds,
  setCheckedIds,
}: Args): SingleDataItem[] {
  const { t } = useTranslation();
  const { hasBackModal, backToModal } = useBackToModal('createEntityPanel');

  const { data: submitVendorData } = useMultiStepFormState('submitToVendor');

  const { data: excludedJobs, isLoading: isLoadingExcluded } =
    useReactQuery<any>({
      action: {
        apiFunc: () => getVendorsExcluded(submitVendorData?.jobId),
        key: [QueryKeys.getVendorsExcluded, String(submitVendorData?.jobId)],
      },
      config: {
        onSuccess: (data) => {
          setCheckedIds(data);
        },
      },
    });

  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title: t('submit_to_vendor'),
    hideBack: !hasBackModal,
    noCloseButton: hasBackModal,
    backButtonProps: {
      onClick: () => {
        backToModal();
        closeMultiStepForm('submitToVendor');
        event.trigger(eventKeys.closeModal);
      },
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <Flex flexDir="column" className="gap-12">
      <Typography
        color="secondaryDisabledText"
        size={12}
        fontWeight={400}
        className="text-end"
      >
        {checkedIds?.length}/{MAXIMUM_SUBMISSION}
      </Typography>
      <TwoButtonFooter
        submitLabel={t('next')}
        secondaryButtonLabel={t('discard')}
        disabledSubmit={!checkedIds?.length}
        onSubmitClick={() => setStep((prev) => prev + 1)}
        secondaryButtonOnClick={() => closeMultiStepForm('submitToVendor')}
      />
    </Flex>
  );

  const handleCheckBox = (id: string, isSelected: boolean) => {
    if (isSelected) {
      if (checkedIds?.length >= MAXIMUM_SUBMISSION) return;
      setCheckedIds((prev) => [...prev, id]);
    } else {
      setCheckedIds((prev) => prev?.filter((val) => val !== id));
    }
  };

  const data: Array<SingleDataItem> = [
    {
      stepKey: '2',
      getHeaderProps,

      renderBody: () => (
        <SearchableAsyncList<SuggestSubmitToVendor>
          variant="multi"
          name={QueryKeys.getSuggestCompany}
          renderInfoMessage={
            <MenuItem
              className="!bg-gray_5 !my-20"
              title={t('maximum_multi_submissions')}
              iconName="info-circle"
            />
          }
          renderItem={({ item }) => (
            <ItemComponent
              key={item?.id}
              image={item?.croppedImageUrl}
              title={item.title}
              subTitle={item?.username}
            >
              <CheckBox
                value={checkedIds.some((val) => item?.id === val)}
                disabled={excludedJobs?.find((val: any) => item?.id === val)}
                onChange={(isSelected: boolean) =>
                  handleCheckBox(item?.id, isSelected)
                }
              />
            </ItemComponent>
          )}
          pageSize={10}
          params={{
            companyFilter: 'VENDOR',
            jobId: submitVendorData?.jobId,
          }}
          normalizer={(values) => values?.content?.map((item: any) => item)}
          keywords="text"
          apiFunc={getSuggestCompany}
          placeholder={t('search_submit_vendor')}
          renderLoading={<SubmitVendorItemSkeleton />}
          renderNextPageLoading={<SubmitVendorItemSkeleton />}
          enableInfiniteScroll
        />
      ),

      renderFooter,
    },
  ];

  return data;
}
